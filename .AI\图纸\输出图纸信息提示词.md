# 图纸信息提取与输出提示词

## 🎯 任务目标
从通信工程图纸中提取关键信息，并按照标准格式输出为Markdown文件。

## � 快速定位指南

### 📍 信息位置速查表
| 信息类型 | 主要位置 | 备用位置 | 特殊说明 |
|---------|---------|---------|---------|
| 铁塔站名 | 图纸第一页说明 | 图纸标题栏 | 去除前后缀 |
| 铁塔编号 | 图纸第一页说明 | 设备标识区 | 确保唯一性 |
| 经度/纬度 | 图纸第一页说明 | 坐标标注 | 保持5位小数 |
| 地址 | 图纸第一页说明 | 位置说明栏 | 完整地址 |
| 图纸名字 | 右下角图框 | - | 提取xxx部分 |
| 杆塔类型 | 天馈示意图说明 | 平面图/立面图 | 使用枚举值 |
| 杆塔高度 | 天馈示意图说明 | 立面图标注 | 纯数值 |
| 天线安装平台 | 立面图 | - | 仅加粗天线 |
| 天线挂高 | 立面图 | - | 仅加粗天线 |

### 🔍 图纸名字识别模式
- **模式1**：xxx基站设备布置及走线图 → 提取：xxx
- **模式2**：xxx基站天馈安装位置示意图 → 提取：xxx
- **模式3**：xxx5G基站设备布置及走线图 → 提取：xxx5G
- **模式4**：xxx5G基站天馈安装位置示意图 → 提取：xxx5G

### ⚠️ 关键识别要点
- **加粗天线原则**：立面图中只有加粗的天线示意图才是本期天线
- **交叉验证原则**：杆塔类型和高度需要说明文字与图形相互印证
- **枚举值限制**：杆塔类型必须严格使用预定义的枚举值

## �📋 提取信息类别

### 1. 图纸基础信息类
需要提取以下关键信息：
- **铁塔站名**：站点名称
- **铁塔编号**：站点编号或基站编号
- **经度**：格式为xx.xxxxx（小数点后5位）
- **纬度**：格式为xx.xxxxx（小数点后5位）
- **地址**：站点详细地址
- **图纸名字**：图纸文件名称
- **杆塔类型**：按照枚举值规则输出（见下方规则）
- **杆塔高度**：数值，省略单位m
- **天线安装平台**：天线安装位置描述
- **天线挂高**：天线安装高度，数值格式

### 2. 图纸表格类
将图纸中的所有表格（除扇区信息表外）转换为标准Markdown格式：

#### 表格处理规则：
- **表格范围**：除扇区信息表外的所有表格都需要转换
- **双行标题处理**：如果表格标题栏有2行，需要合并为1行
  - 合并方式：上行内容+下行内容，用空格或适当连接符连接
  - 示例：上行"设备"，下行"型号" → "设备型号"
- **合并列处理**：如果表格中有合并列，需要补齐填充
  - 填充规则：使用合并列中第一个单元格的值填充所有合并的单元格
  - 示例：原表格中"设备A"跨越3列，转换后每列都填入"设备A"
- **合并行处理**：如果表格中有合并行，需要补齐填充
  - 填充规则：使用合并行中第一个单元格的值填充所有合并的单元格
  - 示例：原表格中"类型1"跨越2行，转换后每行都填入"类型1"
- **空白单元格**：用"-"表示
- **数据对齐**：保持原表格的数据对齐方式
- **表格命名**：每个表格前添加表格标题（如"### 设备清单表"）

### 3. 扇区信息表
将扇区信息表转换为字符串格式：
- **格式**：按参数类型分行，用"/"分隔同类参数的不同扇区数值
- **参数顺序**：
  - 方向角：扇区1方向角/扇区2方向角/扇区3方向角
  - 机械下倾角：扇区1机械下倾/扇区2机械下倾/扇区3机械下倾
  - 电子下倾角：扇区1电子下倾/扇区2电子下倾/扇区3电子下倾
  - 总下倾角：扇区1总下倾/扇区2总下倾/扇区3总下倾
- **示例**：
  ```
  方向角：40/240/300
  机械下倾角：2/2/2
  电子下倾角：2/2/1
  总下倾角：4/4/3
  ```

## 🔧 数据提取规则

### 杆塔类型识别规则
**必须严格按照以下对应关系输出枚举值**：

| 关键字 | 输出杆塔类型 |
|--------|-------------|
| 单管塔 | 通信杆 |
| PVC | 美化排气管 |
| 三管塔 | 铁塔 |
| 角钢塔 | 铁塔 |
| 双支臂 | 支撑杆 |
| xx米抱杆 | 支撑杆 |
| 美化灯杆 | 美化通信杆 |
| 地面抱杆 | 支撑杆 |
| 景观塔 | 通信杆 |

**杆塔类型枚举值列表**：
通信杆、美化通信杆、铁塔、楼面塔、双支臂支撑杆、楼面支撑杆、美化支撑杆、地面支撑杆、超高杆、增高架、美化排气管、美化水桶、美化方柱、美化空调、美化天线外罩、其它

**注意**：若无法识别，输出"无法识别"

### 方向角/下倾角识别规则
- **格式**：xx/xx/xx（3小区）或xx/xx（2小区）或xx（1小区）
- **数值范围**：0-360度
- **获取途径**：备注信息、扇区信息表、设备参数表

### 设备数识别规则
- **识别方式1**：xxRRU或xxAAU，其中xx为0-8的数值
- **识别方式2**：从设备表、布线表中分析设备台数
- **输出**：直接输出数值

### 小区数识别规则
- **方式1**：根据方向角数量确定（xx/xx/xx为3小区）
- **方式2**：读取扇区信息表，1行数据=1个小区

## 📄 输出格式模板

```markdown
# [图纸名称]

> **文件信息**
> 原PDF路径：原目录路径\[原PDF文件名].pdf
> 输出MD路径：映射目录路径\[原PDF文件名].md
> 处理时间：[YYYY-MM-DD HH:mm:ss]

## 📍 基础信息
- **铁塔站名**：[站名]
- **铁塔编号**：[编号]
- **经度**：[xx.xxxxx]
- **纬度**：[xx.xxxxx]
- **地址**：[详细地址]
- **图纸名字**：[图纸文件名]
- **杆塔类型**：[枚举值]
- **杆塔高度**：[数值]
- **天线安装平台**：[平台描述]
- **天线挂高**：[高度数值]

## 📊 图纸表格

### [表格1名称]
| 列1 | 列2 | 列3 |
|-----|-----|-----|
| 数据1 | 数据2 | 数据3 |

### [表格2名称]
| 列1 | 列2 | 列3 |
|-----|-----|-----|
| 数据1 | 数据2 | 数据3 |

## 📡 扇区信息
```
方向角：40/240/300
机械下倾角：2/2/2
电子下倾角：2/2/1
总下倾角：4/4/3
```

## 📝 备注
[其他重要信息或特殊说明]
```

## ⚠️ 重要注意事项

1. **严格按照规则**：必须严格按照数据提取规则进行信息提取
2. **枚举值限制**：杆塔类型必须使用指定的枚举值，不得自创
3. **格式统一**：所有输出必须按照模板格式进行
4. **数据准确**：确保提取的数据准确无误，如有疑问标注"待确认"
5. **路径映射**：必须将原PDF路径`E:\服务器主镜像\辅助目录\PDF自动生成\`映射为`E:\资料存放\AI图纸信息\`
6. **文件命名**：输出文件名与原PDF文件名完全一致，仅更改后缀为.md
7. **目录处理**：确保输出目录存在，不存在时需要创建
8. **文件覆盖**：如生成的.md文件已存在，直接覆盖原文件，无需备份
9. **扇区信息格式**：扇区信息必须按参数类型分行，格式为"参数名：扇区1/扇区2/扇区3"
10. **路径信息**：在输出文件中必须包含完整的原PDF路径和输出MD路径信息
11. **完整性检查**：确保所有必要信息都已提取并填入模板

## 🔍 质量检查清单

### 📍 信息提取位置验证
- [ ] 铁塔站名是否从第一页说明中提取
- [ ] 铁塔编号是否从第一页说明中提取
- [ ] 经纬度是否从第一页说明中提取
- [ ] 地址是否从第一页说明中提取
- [ ] 图纸名字是否从右下角图框中提取
- [ ] 杆塔类型是否从天馈示意图说明中提取
- [ ] 杆塔高度是否从天馈示意图说明中提取
- [ ] 天线信息是否仅从立面图加粗天线中提取

### 📊 数据格式验证
- [ ] 基础信息10项是否完整
- [ ] 普通表格是否正确转换为Markdown格式（除扇区信息表外）
- [ ] 双行标题是否正确合并
- [ ] 合并列是否用第一个单元格值完全填充
- [ ] 合并行是否用第一个单元格值完全填充
- [ ] 扇区信息表是否按参数类型分行转换为字符串格式（不是Markdown表格）
- [ ] 扇区信息格式是否正确（方向角：xx/xx/xx，机械下倾角：xx/xx/xx等）
- [ ] 杆塔类型是否使用正确的枚举值
- [ ] 经纬度格式是否正确（小数点后5位）
- [ ] 图纸名字是否正确提取xxx部分
- [ ] 天线挂高是否仅统计加粗天线

### 🔄 交叉验证检查
- [ ] 杆塔类型：说明文字与平面图/立面图是否一致
- [ ] 杆塔高度：说明文字与立面图标注是否一致
- [ ] 天线信息：是否忽略了非加粗的历史天线

### 📄 输出质量验证
- [ ] 输出文件格式是否符合模板要求
- [ ] 文件路径是否正确映射（E:\服务器主镜像\辅助目录\PDF自动生成\ → E:\资料存放\AI图纸信息\）
- [ ] 文件名是否与原PDF文件名一致（仅后缀改为.md）
- [ ] 输出目录是否存在或已创建
- [ ] 如文件已存在是否正确执行覆盖操作
- [ ] 文件信息块是否包含完整的路径信息
- [ ] 处理时间是否正确标注（包括覆盖标识）
- [ ] 所有必填项是否无遗漏
- [ ] 数据准确性是否经过验证

## 📖 详细操作指南

### 🔍 图纸信息识别技巧

#### 1. 铁塔站名识别
- **主要查找位置**：图纸第一页说明中
- **次要查找位置**：图纸标题栏、项目名称区域
- **常见格式**：XX市XX区XX站、XX基站、XX铁塔等
- **注意事项**：去除多余的前缀后缀，保留核心站名

#### 2. 铁塔编号识别
- **主要查找位置**：图纸第一页说明中
- **次要查找位置**：图纸编号区域、设备标识
- **常见格式**：数字+字母组合、纯数字编号
- **注意事项**：确保编号的唯一性和准确性

#### 3. 经纬度提取
- **主要查找位置**：图纸第一页说明中
- **次要查找位置**：坐标标注、位置信息表
- **格式要求**：必须保留小数点后5位
- **验证方法**：经度范围73-135，纬度范围18-54（中国境内）

#### 4. 地址信息提取
- **主要查找位置**：图纸第一页说明中
- **次要查找位置**：项目信息栏、位置说明
- **完整性要求**：省市区县+详细地址
- **格式统一**：使用标准行政区划名称

#### 5. 图纸名字识别
- **查找位置**：图纸右下角图框中
- **常见格式**：
  - xxx基站设备布置及走线图
  - xxx基站天馈安装位置示意图
  - xxx5G基站设备布置及走线图
  - xxx5G基站天馈安装位置示意图
- **提取规则**：其中xxx即为图纸名，去除后缀描述部分
- **示例**：
  - 原文：东方明珠基站设备布置及走线图 → 提取：东方明珠
  - 原文：科技园5G基站天馈安装位置示意图 → 提取：科技园

#### 6. 杆塔类型识别
- **主要查找位置**：天馈安装位置示意图的说明中
- **辅助验证位置**：图纸的平面图/立面图
- **识别方法**：
  - 优先从说明文字中提取关键字
  - 通过平面图/立面图的塔型结构进行视觉识别
  - 两种方法相互印证，确保准确性
- **注意事项**：必须严格按照枚举值对应关系输出

#### 7. 杆塔高度识别
- **主要查找位置**：天馈安装位置示意图的说明中
- **辅助验证位置**：立面图的标注尺寸
- **识别方法**：
  - 从说明文字中提取高度数值
  - 通过立面图的尺寸标注验证
  - 注意区分总高度和有效高度
- **输出格式**：纯数值，省略单位m

#### 8. 天线安装平台识别
- **主要查找位置**：立面图中
- **识别重点**：加粗的天线示意图才是本期的天线
- **提取内容**：该天线所在的平台位置描述
- **常见描述**：顶部平台、中部平台、侧面支架等
- **注意事项**：忽略非加粗的历史天线或预留天线

#### 9. 天线挂高识别
- **主要查找位置**：立面图中
- **识别重点**：加粗的天线示意图才是本期的天线
- **提取内容**：该天线距离地面的高度数值
- **测量基准**：以地面为0点，向上测量到天线中心位置
- **输出格式**：数值格式，保留小数点后1位
- **注意事项**：
  - 只读取加粗天线的挂高
  - 如有多个加粗天线，分别标注各自挂高
  - 区分天线挂高与平台高度

### 📊 表格转换详细规则

#### 双行标题合并示例：
```
原始表格：
| 设备  | 设备  | 天线 |
| 型号  | 数量  | 类型 |

转换后：
| 设备型号 | 设备数量 | 天线类型 |
```

#### 合并列处理示例：
```
原始表格（合并列）：
| 设备A        | 参数1 | 参数2 |
| 型号1 | 型号2 | 100   | 200   |

转换后（补齐填充）：
| 设备A | 设备A | 参数1 | 参数2 |
| 型号1 | 型号2 | 100   | 200   |
```

#### 合并行处理示例：
```
原始表格（合并行）：
| 类型1 | 设备A | 100 |
|       | 设备B | 200 |
| 类型2 | 设备C | 300 |

转换后（补齐填充）：
| 类型1 | 设备A | 100 |
| 类型1 | 设备B | 200 |
| 类型2 | 设备C | 300 |
```

#### 复杂合并处理示例：
```
原始表格（行列都有合并）：
| 分类     | 设备信息      | 数量 |
| 通信设备 | RRU    | AAU  | 3    |
|          | 型号A  | 型号B | 2    |

转换后（完全补齐）：
| 分类     | 设备信息 | 设备信息 | 数量 |
| 通信设备 | RRU      | AAU      | 3    |
| 通信设备 | 型号A    | 型号B    | 2    |
```

#### 表格处理核心原则：
- **扇区信息表例外**：扇区信息表不转换为Markdown，而是转换为字符串格式
- **标准化原则**：所有其他表格都必须转换为标准Markdown格式
- **补齐原则**：合并单元格必须用第一个单元格的值完全填充
- **空白处理**：统一用"-"表示空白单元格
- **数据完整性**：保持原始数据格式和重要单位信息
- **表格标题**：每个表格前必须添加描述性标题

### 📡 扇区信息字符串转换

#### 标准格式（按参数类型分行）：
```
方向角：扇区1数值/扇区2数值/扇区3数值
机械下倾角：扇区1数值/扇区2数值/扇区3数值
电子下倾角：扇区1数值/扇区2数值/扇区3数值
总下倾角：扇区1数值/扇区2数值/扇区3数值
```

#### 实际示例（基于提供的表格）：
```
方向角：40/240/300
机械下倾角：2/2/2
电子下倾角：2/2/1
总下倾角：4/4/3
```

#### 扇区信息表读取规则：
- **表格识别**：找到标题包含"扇区信息"或类似的表格
- **列识别**：识别扇区编号、方向角、机械下倾角、电子下倾角、总下倾角等列
- **数据提取**：按扇区顺序提取各参数数值
- **格式转换**：将同类参数的不同扇区数值用"/"连接

#### 特殊情况处理：
- **扇区数量不同**：根据实际扇区数量调整（如2扇区：40/240）
- **缺失数据**：用"未知"标注
- **参数缺失**：如无某类下倾角，则该行不输出

## 🚨 常见错误与避免方法

### 1. 杆塔类型错误
- **错误示例**：输出"钢管塔"（非枚举值）
- **正确做法**：查找关键字对应表，输出"通信杆"
- **无法识别**：明确标注"无法识别"

### 2. 经纬度格式错误
- **错误示例**：116.123（小数位不足）
- **正确格式**：116.12300（保持5位小数）
- **验证方法**：检查数值合理性

### 3. 表格转换错误
- **错误示例1**：保留原始的双行标题
- **正确做法1**：合并为单行标题
- **错误示例2**：合并单元格留空或用省略号表示
- **正确做法2**：用第一个单元格的值完全填充所有合并的单元格
- **错误示例3**：扇区信息表转换为Markdown格式
- **正确做法3**：扇区信息表转换为字符串格式，其他表格转换为Markdown
- **格式检查**：确保Markdown表格语法正确

#### 表格处理错误对比示例：
```
❌ 错误处理（合并列未填充）：
| 设备A |       | 参数1 | 参数2 |
| 型号1 | 型号2 | 100   | 200   |

✅ 正确处理（合并列完全填充）：
| 设备A | 设备A | 参数1 | 参数2 |
| 型号1 | 型号2 | 100   | 200   |
```

## 🎯 输出文件路径与命名规范

### 文件路径映射规则：
- **原PDF图纸路径**：`E:\服务器主镜像\辅助目录\PDF自动生成\`
- **输出MD文件路径**：`E:\资料存放\AI图纸信息\`
- **路径映射说明**：将PDF图纸的存储路径映射到AI处理结果的专用目录
- **目录说明**：如不存在对应的目录，则创建该目录

### 文件命名规则：
- **基本格式**：[原PDF图纸名].md
- **文件名保持**：与原PDF图纸文件名完全一致，仅更改后缀名
- **后缀名变更**：.pdf → .md
- **字符处理**：保留原文件名的所有字符（包括特殊字符）
- **路径完整性**：确保输出目录存在，如不存在需要创建

### 完整路径示例：
```
原PDF图纸：E:\服务器主镜像\辅助目录\PDF自动生成\2025年\5G基站\德宏出图\云落寒妈水库西20250808.pdf
输出MD文件：E:\资料存放\AI图纸信息\2025年\5G基站\德宏出图\云落寒妈水库西20250808

原PDF图纸：E:\服务器主镜像\辅助目录\PDF自动生成\2025年\出版整理\7月\RRU\溪西西湖詹20241217.pdf
输出MD文件：E:\资料存放\AI图纸信息\2025年\出版整理\7月\RRU\溪西西湖詹20241217.pdf
```

### 路径处理注意事项：
- **目录结构**：输出文件统一存放在`E:\资料存放\AI图纸信息\`根目录下
- **子目录处理**：即使原PDF在子目录中，输出MD文件也放在根目录
- **文件覆盖规则**：如果生成的站点信息.md文件已存在，直接覆盖原文件
- **目录创建**：如输出目录不存在，需要先创建目录

### 文件覆盖处理规则：
- **覆盖策略**：无条件覆盖已存在的同名.md文件
- **备份说明**：不需要备份原文件，直接覆盖
- **覆盖确认**：处理时明确提示"文件已存在，将进行覆盖"
- **版本管理**：通过处理时间戳区分不同版本的处理结果

## 📋 完整工作流程

### 🔍 第一阶段：图纸结构分析
1. **整体预览**：浏览图纸总页数和基本结构
2. **页面识别**：确认第一页说明、立面图、平面图位置
3. **图框定位**：找到右下角图框位置

### 📍 第二阶段：基础信息提取（按位置优先级）
4. **第一页说明提取**：
   - 铁塔站名
   - 铁塔编号
   - 经度/纬度
   - 地址信息
5. **右下角图框提取**：
   - 图纸名字（提取xxx部分）
6. **天馈安装位置示意图说明提取**：
   - 杆塔类型（关键字识别）
   - 杆塔高度（数值提取）

### 🏗️ 第三阶段：立面图信息提取
7. **天线识别**：
   - 识别加粗的天线示意图（本期天线）
   - 忽略非加粗天线（历史或预留）
8. **平台和挂高提取**：
   - 天线安装平台位置
   - 天线挂高数值（以地面为基准）

### 📊 第四阶段：表格和扇区处理
9. **表格分类识别**：
   - 识别所有表格
   - 区分扇区信息表和其他表格
10. **普通表格转换**（除扇区信息表外）：
    - 处理双行标题合并
    - 补齐合并列（用第一个单元格值填充）
    - 补齐合并行（用第一个单元格值填充）
    - 转换为标准Markdown格式
11. **扇区信息表特殊处理**：
    - 不转换为Markdown表格
    - 按参数类型分行转换为字符串格式：
      - 方向角：扇区1/扇区2/扇区3
      - 机械下倾角：扇区1/扇区2/扇区3
      - 电子下倾角：扇区1/扇区2/扇区3
      - 总下倾角：扇区1/扇区2/扇区3

### ✅ 第五阶段：验证和输出
11. **交叉验证**：
    - 杆塔类型：说明文字 ↔ 平面图/立面图
    - 杆塔高度：说明文字 ↔ 立面图标注
    - 天线信息：立面图加粗标识确认
12. **数据格式化**：
    - 经纬度保持5位小数
    - 高度数值去除单位
    - 杆塔类型使用枚举值
13. **质量检查**：使用检查清单进行最终验证
14. **路径映射**：将原PDF路径`E:\服务器主镜像\辅助目录\PDF自动生成\`映射为`E:\资料存放\AI图纸信息\`
15. **文件存在检查**：检查目标.md文件是否已存在
16. **文件输出**：
    - 保持原PDF文件名，仅更改后缀为.md
    - 确保输出目录存在，不存在则创建
    - 如文件已存在，直接覆盖（无需备份）
    - 生成完整路径的Markdown文件
    - 在处理时间戳中记录覆盖操作

### 🎯 关键查找优先级总结
```
优先级1：图纸第一页说明
├── 铁塔站名
├── 铁塔编号
├── 经度/纬度
└── 地址

优先级2：图纸右下角图框
└── 图纸名字

优先级3：天馈安装位置示意图说明
├── 杆塔类型
└── 杆塔高度

优先级4：立面图
├── 天线安装平台（加粗天线）
└── 天线挂高（加粗天线）

优先级5：平面图/立面图（验证用）
├── 杆塔类型验证
└── 杆塔高度验证
```