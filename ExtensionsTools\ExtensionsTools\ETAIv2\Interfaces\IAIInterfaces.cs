using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using ET.ETAIv2.Models;
using ET.ETAIv2.Exceptions;

namespace ET.ETAIv2.Interfaces
{
    /// <summary>
    /// AI处理管理器接口
    /// </summary>
    public interface IAIProcessingManager
    {
        /// <summary>
        /// 处理AI请求
        /// </summary>
        Task<AIResponse> ProcessAsync(
            AIDataSourceConfig config,
            IProgress<ProcessingProgress> progress = null,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// 取消所有正在进行的请求
        /// </summary>
        void CancelAllRequests();
    }

    /// <summary>
    /// 数据提取器接口
    /// </summary>
    public interface IAIDataExtractor
    {
        /// <summary>
        /// 从Excel范围提取数据组
        /// </summary>
        List<AIDataGroup> ExtractDataGroups(AIDataSourceConfig config);

        /// <summary>
        /// 提取列级提示词
        /// </summary>
        Dictionary<string, string> ExtractColumnPrompts(AIDataSourceConfig config);
    }

    /// <summary>
    /// 文件处理器接口
    /// </summary>
    public interface IAIFileProcessor
    {
        /// <summary>
        /// 本地读取文件内容
        /// </summary>
        Task<FileData> ReadFileLocallyAsync(string filePath, CancellationToken cancellationToken = default);

        /// <summary>
        /// 批量处理文件
        /// </summary>
        Task<List<FileData>> ProcessFilesAsync(
            List<string> filePaths,
            FileProcessingMode mode,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// 验证文件是否支持
        /// </summary>
        bool IsFileSupported(string filePath);
    }

    /// <summary>
    /// AI客户端接口
    /// </summary>
    public interface IAIClient
    {
        /// <summary>
        /// 发送Chat API请求
        /// </summary>
        Task<AIResponse> SendChatRequestAsync(
            AIRequest request,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// 发送AI请求（统一使用Chat API）
        /// </summary>
        Task<AIResponse> SendRequestAsync(
            AIRequest request,
            CancellationToken cancellationToken = default);
    }

    /// <summary>
    /// 结果回填器接口
    /// </summary>
    public interface IAIResultFiller
    {
        /// <summary>
        /// 将AI结果回填到Excel
        /// </summary>
        Task FillResultsAsync(AIResponse response, AIDataSourceConfig config);

        /// <summary>
        /// 验证回填数据的有效性
        /// </summary>
        bool ValidateResults(AIResponse response, AIDataSourceConfig config);
    }

    /// <summary>
    /// 配置管理器接口
    /// </summary>
    public interface IAIConfigManager
    {
        /// <summary>
        /// 加载模型配置
        /// </summary>
        AIModelConfig LoadModelConfig(string configFile);

        /// <summary>
        /// 加载全局提示词
        /// </summary>
        string LoadGlobalPrompt(string promptFile);

        /// <summary>
        /// 保存配置
        /// </summary>
        void SaveConfig(string configFile, AIModelConfig config);

        /// <summary>
        /// 获取配置文件路径
        /// </summary>
        string GetConfigPath(string fileName);

        /// <summary>
        /// 列出可用的配置文件
        /// </summary>
        List<string> ListConfigFiles(string pattern = "*.ai");

        /// <summary>
        /// 列出可用的规则文件
        /// </summary>
        List<string> ListRuleFiles(string pattern = "*.rule");

        /// <summary>
        /// 异步获取API密钥
        /// </summary>
        Task<string> GetApiKeyAsync();

        /// <summary>
        /// 异步获取基础URL
        /// </summary>
        Task<string> GetBaseUrlAsync();

        /// <summary>
        /// 验证API配置是否有效
        /// </summary>
        Task<bool> ValidateApiConfigAsync();
    }

    /// <summary>
    /// 错误处理器接口
    /// </summary>
    public interface IAIErrorHandler
    {
        /// <summary>
        /// 执行带重试的操作
        /// </summary>
        Task<T> ExecuteWithRetryAsync<T>(
            Func<Task<T>> operation,
            int maxRetries = 3,
            TimeSpan delay = default,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// 判断是否应该重试
        /// </summary>
        bool ShouldRetry(Exception exception);

        /// <summary>
        /// 记录错误
        /// </summary>
        void LogError(Exception exception, string context = null);

        /// <summary>
        /// 包装异常为AI处理异常
        /// </summary>
        AIProcessingException WrapException(Exception exception, string context = null);

        /// <summary>
        /// 安全执行操作，捕获并包装异常
        /// </summary>
        Task<T> SafeExecuteAsync<T>(Func<Task<T>> operation, string context = null);
    }

    /// <summary>
    /// 日志记录器接口
    /// </summary>
    public interface IAILogger
    {
        /// <summary>
        /// 记录信息
        /// </summary>
        void LogInfo(string message, params object[] args);

        /// <summary>
        /// 记录警告
        /// </summary>
        void LogWarning(string message, params object[] args);

        /// <summary>
        /// 记录错误
        /// </summary>
        void LogError(string message, Exception exception = null, params object[] args);

        /// <summary>
        /// 记录调试信息
        /// </summary>
        void LogDebug(string message, params object[] args);

        /// <summary>
        /// 记录性能信息
        /// </summary>
        void LogPerformance(string operation, TimeSpan duration, object additionalInfo = null);

        /// <summary>
        /// 记录API调用信息
        /// </summary>
        void LogAPICall(string apiType, string endpoint, TimeSpan duration, bool success, string errorMessage = null);

        /// <summary>
        /// 记录文件处理信息
        /// </summary>
        void LogFileProcessing(string operation, string filePath, long fileSize, bool success, string errorMessage = null);

        /// <summary>
        /// 记录Excel操作信息
        /// </summary>
        void LogExcelOperation(string operation, string rangeAddress, int rowCount, int columnCount, bool success, string errorMessage = null);
    }
}
